import { ref, reactive, computed, watch } from 'vue'
import vehicleOrderApi from '@/api/vehicleOrder'
import salesPriceLimitRuleApi from '@/api/salesPriceLimitRule'
import messages from '@/utils/messages'
import { convertNumberToChinese, convertCentsToYuan } from '@/utils/money'
import { useAdvancedDictOptions } from '@/composables/useAdvancedDict'
import { getPageDictConfig } from '@/config/dictConfig'
import { DICT_CODES } from '@/constants/dictConstants'
import { getOrderFormRules } from '@/utils/orderFormRules'
import { calculateSalesPrice, calculateProfitRate, calculateLoanAmounts, LOAN_RULES } from '@/utils/businessRules'

/**
 * 订单编辑弹窗组合式函数
 * @param {Object} props - 组件属性
 * @param {Function} emit - 组件事件发射器
 * @returns {Object} 返回响应式数据和方法
 */
export function useOrderEditModal(props, emit) {
  // ==================== 响应式数据 ====================

  // 组件状态
  const formRef = ref(null)
  const customerSelectorVisible = ref(false)
  const vehicleSelectorVisible = ref(false)
  const giftItemsSectionRef = ref(null)
  const remarkSectionRef = ref(null)

  // 出库单位选择器相关
  const selectedOutboundOrg = ref(null)

  // 销售限价相关状态
  const currentPriceLimit = ref(null) // 当前车型的销售限价（单位：分）

  // 获取页面字典配置
  const pageConfig = getPageDictConfig('ORDER_EDIT')

  // 贷款渠道选项 - 使用新的字典抽象架构
  const {
    options: loanChannelOptions,
    loading: loanChannelLoading
  } = useAdvancedDictOptions(
    DICT_CODES.LOAN_CHANNEL,
    pageConfig.loanChannel?.config || {}
  )

  // 贷款期限选项 - 使用新的字典抽象架构
  const {
    options: loanMonthsOptions,
    loading: loanMonthsLoading
  } = useAdvancedDictOptions(
    DICT_CODES.LOAN_MONTHS,
    pageConfig.loanMonths?.config || {}
  )

  // 计算属性：模态框可见性
  const modelVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
  })

  // 计算属性：专项优惠配置
  const exclusiveDiscountConfig = computed(() => {
    return {
      visible: true,
      editable: true,
      fields: {
        // 在新增订单时隐藏"应收厂家专项优惠"字段
        exclusiveDiscountReceivableAmount: {
          visible: props.isEdit, // 只有在编辑模式下才可见
          editable: true
        }
      }
    }
  })

  // ==================== 工具函数 ====================

  // 处理日期转换
  const convertDateToTimestamp = (date) => {
    if (!date) return null
    if (typeof date === 'number') return date
    if (date instanceof Date) return date.getTime()
    return null
  }

  // ==================== 表单数据 ====================

  // 获取表单默认值的函数
  const getFormDefaultValues = () => ({
    id: null,
    dealDate: Date.now(), // 默认为当前日期
    salesOrgId: null, // 销售单位ID
    salesOrgName: '',

    // 出库信息
    outboundOrgId: null, // 出库单位ID
    outboundOrgName: '', // 出库单位名称
    expectedOutboundDate: Date.now(), // 默认为当前日期

    // 客户信息
    customerId: null,
    customerName: '',
    customerType: 'individual', // 客户类型，默认为个人客户
    customerPhone: '',
    salesAgentName: '',
    salesAgentId: null, // 销售顾问ID
    salesLeaderId: null, // 销售主管ID
    salesStoreType: '4S', // 销售地类型，默认为单店

    // 车辆信息
    skuId: null, // 车辆SKU ID
    vehicleBrand: '', // 车辆品牌
    vehicleSeries: '', // 车型
    vehicleConfig: '', // 配置
    vehicleColorCode: '', // 颜色代码
    vehicleSbPrice: 0, // 启票价格
    invoicePrice: 0, // 开票价格
    salesAmount: 0, // 销售价格

    // 金额信息
    depositAmount: 0, // 已付定金
    depositDeductible: true, // 定金是否转车款，默认为是
    depositType: 'offline', // 定金类型，默认为线下定金
    discountAmount: 0, // 优惠金额
    discountDeductible: true, // 优惠金额是否转车款，默认为是
    hasExclusiveDiscount: 'NO', // 是否享受专项优惠补贴，默认为"否"
    exclusiveDiscountType: '', // 专享优惠类型，默认为"无"
    exclusiveDiscountAmount: 0, // 专享优惠金额（保留兼容性）
    exclusiveDiscountPayableDeductible: false, // 专享优惠是否转车款（保留兼容性）
    exclusiveDiscountReceivableAmount: 0, // 应收-厂家-专项优惠金额
    exclusiveDiscountPayableAmount: 0, // 应付-客户-专项优惠金额
    exclusiveDiscountPayableDeductible: true, // 应付客户专项优惠是否转车款，默认为是
    exclusiveDiscountRemark: '', // 专项优惠备注
    dealAmount: 0, // 成交总价
    dealAmountCn: '零', // 成交总价（大写）
    profitRate: 0, // 预估毛利率
    grossProfitAmount: 0, // 预估毛利润

    // 付款方式
    paymentMethod: 'FULL', // 默认全款，可选值：FULL（全款）、LOAN（贷款）

    // 贷款信息 - 付款方式为贷款时使用
    loanChannel: '', // 贷款渠道
    loanAmount: 0, // 贷款金额
    loanInitialAmount: 0, // 首付金额
    loanInitialRatio: 0, // 首付比例
    loanMonths: null, // 贷款期限（月）
    loanRebateAmount: 0, // 分期返利（客户）
    loanRebatePayableDeductible: true, // 分期返利是否转车款，默认为是
    loanFee: 0, // 分期服务费，默认为0
    loanRebateReceivableAmount: 0, // 应收-机构-分期返利
    loanRebatePayableAmount: 0, // 应付-客户-分期返利
    loanRebatePayableDeductible: true, // 应付客户分期返利是否转车款，默认为是

    // 二手车置换信息
    hasUsedVehicle: 'NO', // 是否有车辆置换，默认为"无"，可选值：YES（有）、NO（无）
    usedVehicleId: '', // 二手车车牌号
    usedVehicleVin: '', // 二手车VIN
    usedVehicleAmount: 0, // 二手车置换金额
    usedVehicleDeductibleAmount: 0, // 转车款金额
    usedVehicleDiscountReceivableAmount: 0, // 应收-厂家-置换补贴
    usedVehicleDiscountPayableAmount: 0, // 应付-客户-置换补贴
    usedVehicleDiscountPayableDeductible: true, // 应付客户置换补贴是否转车款，默认为是
    usedVehicleBrand: '', // 二手车品牌
    usedVehicleModel: '', // 二手车车型
    usedVehicleColor: '', // 二手车颜色



    // 赠品明细信息
    hasGiftItems: 'NO', // 是否有赠品，默认为"无"，可选值：YES（有）、NO（无）
    giftItems: [], // 赠品明细数组

    // 衍生收入信息（用于财务结算显示，但在销售订单中不显示输入组件）
    hasDerivativeIncome: 'NO', // 是否有衍生收入，默认为"无"，可选值：YES（有）、NO（无）
    notaryFee: 0, // 公证费
    carefreeIncome: 0, // 畅行无忧收入
    extendedWarrantyIncome: 0, // 延保收入
    vpsIncome: 0, // VPS收入
    preInterest: 0, // 前置利息
    licensePlateFee: 0, // 挂牌费
    tempPlateFee: 0, // 临牌费
    deliveryEquipment: 0, // 外卖装具收入
    otherIncome: 0, // 其他收入

    // 备注信息
    remark: '', // 订单备注
    paymentRemark: '' // 付款备注（保留兼容性）
  })

  // 表单数据 - 使用默认值函数初始化
  const form = reactive(getFormDefaultValues())

  // 初始化表单数据
  if (props.initialData) {
    const data = { ...props.initialData }

    // 确保日期是时间戳格式
    if (data.dealDate) {
      data.dealDate = convertDateToTimestamp(data.dealDate)
    }

    Object.assign(form, data)
  }

  // 表单验证规则 - 使用从工具函数导入的校验规则
  const rules = computed(() => getOrderFormRules(form))

  // ==================== 基础方法 ====================

  // 更新可见性
  const updateVisible = (val) => {
    emit('update:visible', val)
  }

  // 显示客户选择器
  const showCustomerSelector = () => {
    customerSelectorVisible.value = false;
  }

  // 显示车辆选择器
  const showVehicleSelector = () => {
    vehicleSelectorVisible.value = true
  }

  // ==================== 计算函数 ====================

  // 计算成交总价
  const calculateFinalPrice = () => {
    // 基础成交价 = 销售价格
    let basePrice = form.salesAmount || 0
    let calculationLog = [`车辆售价: ${basePrice}`]

    // 1. 如果有现金优惠且勾选了转车款选项，减去现金优惠金额
    if (form.discountAmount > 0 && form.discountDeductible) {
      basePrice = Math.max(0, basePrice - (form.discountAmount || 0))
      calculationLog.push(`- 现金优惠(已转): ${form.discountAmount}`)
    }

    // 2. 如果已付定金且勾选了转车款选项，减掉定金金额
    if (form.depositAmount > 0 && form.depositDeductible) {
      basePrice = Math.max(0, basePrice - (form.depositAmount || 0))
      calculationLog.push(`- 已付定金(已转): ${form.depositAmount}`)
    }

    // 3. 如果是分期付款，处理分期相关计算
    if (form.paymentMethod === 'LOAN') {
      // 如果应付-客户-分期返利且勾选了转车款选项，减去应付客户分期返利金额
      if (form.loanRebatePayableAmount > 0 && form.loanRebatePayableDeductible) {
        basePrice = Math.max(0, basePrice - (form.loanRebatePayableAmount || 0))
        calculationLog.push(`- 应付-客户-分期返利(已转): ${form.loanRebatePayableAmount}`)
      }

      // 注意：分期服务费是独立的收入项，不影响车辆成交价，因此不在此处计算
      if (form.loanFee > 0) {
        calculationLog.push(`[分期服务费: ${form.loanFee}元，独立收入项，不影响成交价]`)
      }
    }

    // 4. 如果有车辆置换，处理置换相关计算
    if (form.hasUsedVehicle === 'YES') {
      // 如果应付-客户-置换补贴且勾选了转车款选项，减去应付客户置换补贴金额
      if (form.usedVehicleDiscountPayableAmount > 0 && form.usedVehicleDiscountPayableDeductible) {
        basePrice = Math.max(0, basePrice - (form.usedVehicleDiscountPayableAmount || 0))
        calculationLog.push(`- 应付-客户-置换补贴(已转): ${form.usedVehicleDiscountPayableAmount}`)
      }

      // 置换转车款：只减去转车款部分，差价部分是另外支付给客户的现金
      const usedVehicleDeductibleAmount = form.usedVehicleDeductibleAmount || 0
      if (usedVehicleDeductibleAmount > 0) {
        basePrice = Math.max(0, basePrice - usedVehicleDeductibleAmount)
        calculationLog.push(`- 置换转车款: ${usedVehicleDeductibleAmount}`)
      }

      // 显示置换差价信息（仅用于日志，不参与车款计算）
      const usedVehicleAmount = form.usedVehicleAmount || 0
      const exchangeDifference = usedVehicleAmount - usedVehicleDeductibleAmount
      if (exchangeDifference > 0) {
        calculationLog.push(`[置换差价${exchangeDifference}元将以现金支付给客户]`)
      }
    }

    // 5. 如果应付-客户-专项优惠且勾选了转车款选项，减去应付客户专项优惠金额
    if (form.exclusiveDiscountPayableAmount > 0 && form.exclusiveDiscountPayableDeductible) {
      basePrice = Math.max(0, basePrice - (form.exclusiveDiscountPayableAmount || 0))
      calculationLog.push(`- 应付-客户-专项优惠(已转): ${form.exclusiveDiscountPayableAmount}`)
    }

    form.dealAmount = basePrice
    // 更新大写金额
    form.dealAmountCn = convertNumberToChinese(form.dealAmount)

    // 打印价格计算日志
    console.log('=== 价格计算逻辑 ===')
    console.log(calculationLog.join('\n'))
    console.log(`= 应付总额: ${form.dealAmount}`)
    console.log('==================')
  }

  // 计算预估毛利率和毛利润
  const calculateProfitRateAndAmount = () => {
    // 计算总成本 = 启票价格（销售费用字段已移除）
    const totalCost = form.sbAmount

    // 计算预估毛利润 = 成交总价 - 总成本
    form.grossProfitAmount = form.dealAmount - totalCost

    // 使用业务规则计算毛利率
    form.profitRate = calculateProfitRate(form.dealAmount, totalCost)
  }

  // ==================== 事件处理函数 ====================

  // 查询销售限价
  const fetchPriceLimit = async (skuId, salesOrgId) => {
    if (!skuId || !salesOrgId) {
      currentPriceLimit.value = null
      return
    }

    try {
      const response = await salesPriceLimitRuleApi.getPriceLimitBySku(skuId, salesOrgId)
      if (response && response.data && response.data.priceLimit) {
        currentPriceLimit.value = response.data.priceLimit // 保存限价（单位：分）
      } else {
        currentPriceLimit.value = null
      }
    } catch (error) {
      console.error('查询销售限价失败:', error)
      currentPriceLimit.value = null
    }
  }

  // 处理车辆选择
  const handleVehicleSelected = (vehicle) => {
    if (!vehicle) return

    // 使用业务规则计算销售价格
    const salesPrice = calculateSalesPrice(vehicle.sbPrice, vehicle.brand)

    // 更新表单数据，但保留其他字段的值
    const updatedForm = {
      ...form,
      skuId: Number(vehicle.id), // 确保 skuId 是数字类型
      vehicleBrand: vehicle.brand || '',
      vehicleSeries: vehicle.series || '',
      vehicleConfig: vehicle.configName || '',
      vehicleColorCode: vehicle.colorCode || '',
      sbAmount: vehicle.sbPrice || 0,
      salesAmount: salesPrice,
      invoicePrice: salesPrice, // 开票价格默认与车辆售价保持一致
      // 清空出库单位相关数据
      deliveryOrgId: null,
      outboundOrgName: ''
    }

    // 查询销售限价
    if (updatedForm.skuId && updatedForm.salesOrgId) {
      fetchPriceLimit(updatedForm.skuId, updatedForm.salesOrgId)
    }

    // 使用 Object.assign 更新表单数据
    Object.assign(form, updatedForm)

    // 触发相关计算
    handleSalePriceChange()
  }

  // 处理开票价格变化
  const handleInvoicePriceChange = () => {
    // 开票价格变化时可能需要重新计算相关金额
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  }

  // 处理销售价格变化
  const handleSalePriceChange = () => {
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  }

  // 处理优惠金额变化
  const handleDiscountChange = () => {
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  }



  // 处理专享优惠变化
  const handleExclusiveDiscountChange = () => {
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  }

  // 处理应收厂家专项优惠金额变化
  const handleExclusiveDiscountReceivableAmountChange = () => {
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  }

  // 处理应付客户专项优惠金额变化
  const handleExclusiveDiscountPayableAmountChange = () => {
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  }

  // 处理应付客户专项优惠转车款选项变化
  const handleExclusiveDiscountPayableDeductibleChange = () => {
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  }



  // 处理贷款金额变化
  const handleLoanAmountChange = () => {
    const dealAmount = form.dealAmount || 0
    if (dealAmount > 0) {
      // 当贷款金额变化时，自动计算首付金额 = 成交金额 - 贷款金额
      form.loanInitialAmount = parseFloat((dealAmount - form.loanAmount).toFixed(2))
      // 确保首付金额不小于0
      if (form.loanInitialAmount < 0) {
        form.loanInitialAmount = 0
        // 如果首付金额被调整为0，则贷款金额等于成交金额
        form.loanAmount = dealAmount
      }
      // 计算首付比例 = 首付金额 / 成交金额 * 100%
      form.loanInitialRatio = parseFloat(((form.loanInitialAmount / dealAmount) * 100).toFixed(2))
    }
  }

  // 处理首付金额变化
  const handleLoanInitialAmountChange = () => {
    const dealAmount = form.dealAmount || 0
    if (dealAmount > 0) {
      // 当首付金额变化时，自动计算贷款金额 = 成交金额 - 首付金额
      form.loanAmount = parseFloat((dealAmount - form.loanInitialAmount).toFixed(2))
      // 确保贷款金额不小于0
      if (form.loanAmount < 0) {
        form.loanAmount = 0
        // 如果贷款金额被调整为0，则首付金额等于成交金额
        form.loanInitialAmount = dealAmount
      }
      // 计算首付比例 = 首付金额 / 成交金额 * 100%
      form.loanInitialRatio = parseFloat(((form.loanInitialAmount / dealAmount) * 100).toFixed(2))
    }
  }

  // 处理分期服务费变化
  const handleLoanFeeChange = () => {
    // 分期服务费是独立的收入项，不影响车辆成交价和分期金额计算
    // 因此这里不需要重新计算成交价格和分期相关金额
    // 只需要重新计算毛利率（因为分期服务费会影响总收入）
    calculateProfitRateAndAmount()
  }

  // 基于成交金额重新计算分期相关金额
  // 注意：成交金额 = 车辆售价 - 所有转车款项目，不包含独立收入项（如分期服务费）
  const recalculateLoanAmountsBasedOnDealAmount = () => {
    const dealAmount = form.dealAmount || 0
    if (dealAmount > 0 && form.paymentMethod === 'LOAN') {
      if (form.loanInitialRatio > 0) {
        // 如果已有首付比例，保持比例不变，重新计算金额
        const ratio = form.loanInitialRatio / 100
        form.loanInitialAmount = parseFloat((dealAmount * ratio).toFixed(2))
        form.loanAmount = parseFloat((dealAmount - form.loanInitialAmount).toFixed(2))
      } else {
        // 如果没有首付比例，使用默认比例（30%首付）
        const loanAmounts = calculateLoanAmounts(dealAmount)
        form.loanInitialRatio = loanAmounts.downPaymentRatio
        form.loanInitialAmount = loanAmounts.downPaymentAmount
        form.loanAmount = loanAmounts.loanAmount
      }
    }
  }

  // 处理应付客户分期返利金额变化
  const handleLoanRebatePayableAmountChange = () => {
    // 当应付客户分期返利金额变化时，可能需要重新计算成交价格
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  }

  // 处理应付客户分期返利转车款选项变化
  const handleLoanRebatePayableDeductibleChange = () => {
    // 当转车款选项变化时，重新计算成交价格
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  }

  // 处理转车款金额变化
  const handleUsedVehicleDeductibleAmountChange = () => {
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  }

  // 处理应付客户置换补贴金额变化
  const handleUsedVehicleDiscountPayableAmountChange = () => {
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  }

  // 处理应付客户置换补贴转车款选项变化
  const handleUsedVehicleDiscountPayableDeductibleChange = () => {
    // 当转车款选项变化时，重新计算成交价格
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  }



  // 处理出库单位变更
  const handleOutboundOrgChange = (org) => {
    // 更新选中的出库单位
    selectedOutboundOrg.value = org

    if (org) {
      // BizOrgSelector返回的字段是orgName，需要正确映射
      form.outboundOrgId = org.id
      form.outboundOrgName = org.orgName || org.name || ''
    } else {
      // 清空出库单位数据
      form.outboundOrgId = null
      form.outboundOrgName = ''
    }
  }

  // 处理客户选择
  const handleCustomerSelected = (customer) => {
    // 更新客户信息
    form.customerId = customer.id
    form.customerName = customer.customerName
    form.customerType = customer.customerType || 'individual' // 客户类型，默认为个人客户
    form.customerPhone = customer.mobile
    form.salesAgentName = customer.ownerSellerName

    // 设置销售顾问和销售主管ID
    form.salesAgentId = customer.ownerSellerId || customer.id
    form.salesLeaderId = customer.ownerLeaderId || customer.id

    // 更新销售单位信息（无论新增还是编辑都需要更新）
    form.salesOrgId = customer.ownerOrgId || null
    form.salesOrgName = customer.ownerOrgName || ''
  }

  // ==================== 监听器设置 ====================

  // 监听车辆信息变化，自动计算金额
  watch([() => form.sbAmount, () => form.salesAmount], () => {
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  })

  // 监听车辆售价变化，自动同步开票价格
  watch(() => form.salesAmount, (newSalePrice) => {
    if (newSalePrice > 0) {
      // 当车辆售价变化时，自动同步开票价格
      form.invoicePrice = newSalePrice
    }
  })



  // 监听已付定金和转车款选项变化，自动计算金额
  watch([() => form.depositAmount, () => form.depositDeductible], () => {
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  })

  // 监听优惠金额和转车款选项变化，自动计算金额
  watch([() => form.discountAmount, () => form.discountDeductible], () => {
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  })

  // 监听应付客户分期返利和转车款选项变化
  watch([() => form.loanRebatePayableAmount, () => form.loanRebatePayableDeductible], () => {
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  })

  // 监听应付客户专项优惠和转车款选项变化
  watch([() => form.exclusiveDiscountPayableAmount, () => form.exclusiveDiscountPayableDeductible], () => {
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  })

  // 监听应付客户置换补贴和转车款选项变化
  watch([() => form.usedVehicleDiscountPayableAmount, () => form.usedVehicleDiscountPayableDeductible], () => {
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  })

  // 监听置换金额和转车款金额变化，重新计算置换差价
  watch([() => form.usedVehicleAmount, () => form.usedVehicleDeductibleAmount], () => {
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  })

  // 监听车辆销售价格变化，重新计算成交价格和分期金额
  // 车辆售价变化会影响成交金额，进而影响分期计算
  watch(() => form.salesAmount, (newValue, oldValue) => {
    // 车辆售价变化时，重新计算成交价格
    calculateFinalPrice()
    calculateProfitRateAndAmount()

    // 分期相关金额会在calculateFinalPrice中自动重新计算
  })

  // 监听付款方式变化
  watch(() => form.paymentMethod, (newValue, oldValue) => {
    // 如果从分期切换到全款，需要重新计算成交价格（去掉分期服务费的影响）
    if (oldValue === 'LOAN' && newValue === 'FULL' && form.loanFee > 0) {
      // 清空分期服务费
      form.loanFee = 0
      // 重新计算成交价格
      calculateFinalPrice()
      calculateProfitRateAndAmount()
    }

    if (newValue === 'LOAN' && form.dealAmount > 0) {
      // 如果切换到贷款方式，且成交金额大于0，设置默认的贷款金额和首付金额
      if (form.loanInitialAmount === 0 && form.loanAmount === 0) {
        // 使用业务规则设置默认值，基于成交金额
        const loanAmounts = calculateLoanAmounts(form.dealAmount)
        form.loanInitialRatio = loanAmounts.downPaymentRatio
        form.loanInitialAmount = loanAmounts.downPaymentAmount
        form.loanAmount = loanAmounts.loanAmount
      }

      // 重置分期返利相关字段
      form.loanRebateAmount = 0
      form.loanRebatePayableDeductible = true

      // 重新计算成交价格和分期金额
      calculateFinalPrice()
      calculateProfitRateAndAmount()
    }

    // 当切换到贷款方式时，始终确保loanMonths有值，避免验证错误
    if (newValue === 'LOAN') {
      // 如果没有选择分期期数，使用业务规则默认值
      if (form.loanMonths === null || form.loanMonths === undefined || form.loanMonths === '') {
        form.loanMonths = LOAN_RULES.DEFAULT_LOAN_MONTHS
      }
    }
  })

  // 监听车辆置换选项变化
  watch(() => form.hasUsedVehicle, (newValue) => {
    if (newValue === 'NO') {
      // 如果切换到"无"，清空二手车信息
      form.usedVehicleId = ''
      form.usedVehicleVin = ''
      form.usedVehicleAmount = 0
      form.usedVehicleDeductibleAmount = 0
      form.usedVehicleDiscountReceivableAmount = 0
      form.usedVehicleDiscountPayableAmount = 0
      form.usedVehicleDiscountPayableDeductible = true
      form.usedVehicleBrand = ''
      form.usedVehicleModel = ''
      form.usedVehicleColor = ''

      // 重新计算成交价格
      calculateFinalPrice()
      calculateProfitRateAndAmount()
    }
  })

  // 监听转车款金额变化
  watch(() => form.usedVehicleDeductibleAmount, () => {
    calculateFinalPrice()
    calculateProfitRateAndAmount()
  })

  // 监听成交金额变化，自动更新分期相关金额
  watch(() => form.dealAmount, (newDealAmount, oldDealAmount) => {
    // 只有在分期付款模式下且成交金额确实发生变化时才重新计算
    if (form.paymentMethod === 'LOAN' && newDealAmount !== oldDealAmount && newDealAmount > 0) {
      // 延迟执行，避免在calculateFinalPrice执行过程中重复计算
      setTimeout(() => {
        recalculateLoanAmountsBasedOnDealAmount()
      }, 0)
    }
  })

  // 监听SKU和销售机构变化，重新查询销售限价
  watch([() => form.skuId, () => form.salesOrgId], ([newSkuId, newSalesOrgId]) => {
    if (newSkuId && newSalesOrgId) {
      fetchPriceLimit(newSkuId, newSalesOrgId)
    } else {
      currentPriceLimit.value = null
    }
  })



  // 监听赠品明细选项变化
  watch(() => form.hasGiftItems, (newValue) => {
    if (newValue === 'NO') {
      // 如果切换到"无"，清空赠品明细数组
      form.giftItems = []
      // 如果表格引用存在，调用清空方法
      if (giftItemsSectionRef.value?.giftItemsTableRef) {
        giftItemsSectionRef.value.giftItemsTableRef.clearRows()
      }
    } else if (newValue === 'YES' && form.giftItems.length === 0) {
      // 如果切换到"有"且赠品数组为空，添加一个空行
      if (giftItemsSectionRef.value?.giftItemsTableRef) {
        giftItemsSectionRef.value.giftItemsTableRef.addRow()
      }
    }
  })

  // ==================== 保存和重置函数 ====================

  // 处理保存
  const handleSave = () => {
    // 如果是分期付款方式，确保分期期数有值
    if (form.paymentMethod === 'LOAN' && (form.loanMonths === null || form.loanMonths === undefined || form.loanMonths === '')) {
      form.loanMonths = LOAN_RULES.DEFAULT_LOAN_MONTHS
    }

    // 如果是分期付款方式，验证首付+分期金额是否等于成交金额
    if (form.paymentMethod === 'LOAN') {
      const totalLoanAmount = (form.loanInitialAmount || 0) + (form.loanAmount || 0)
      const dealAmount = form.dealAmount || 0
      const difference = Math.abs(totalLoanAmount - dealAmount)

      // 允许1分钱的误差（由于浮点数计算精度问题）
      if (difference > 0.01) {
        return messages.error(`分期金额验证失败：首付金额(${form.loanInitialAmount})+ 贷款金额(${form.loanAmount})= ${totalLoanAmount}，但成交金额为${dealAmount}，两者必须相等`)
      }
    }

    // 销售限价检查
    if (currentPriceLimit.value && form.salesAmount) {
      const salesAmountInCents = Math.round(form.salesAmount * 100) // 将车辆售价从元转换为分
      if (salesAmountInCents < currentPriceLimit.value) {
        const priceLimitInYuan = (currentPriceLimit.value / 100).toFixed(2) // 将限价从分转换为元
        return messages.error(`车辆售价不得低于销售限价${priceLimitInYuan}元`)
      }
    }

    // 执行表单验证
    formRef.value?.validate(async (errors, _) => {
      if (errors) {
        return messages.error('请完善表单信息')
      }

      try {
        // 构建保存数据
        const data = { ...form }

        // 获取合并后的备注数据
        if (remarkSectionRef.value?.getUpdatedRemarkData) {
          data.remark = remarkSectionRef.value.getUpdatedRemarkData()
        }

        // 确保日期是时间戳格式
        if (data.dealDate) {
          data.dealDate = convertDateToTimestamp(data.dealDate)
        }

        // 确保预计出库日期是时间戳格式
        if (data.expectedOutboundDate) {
          data.expectedOutboundDate = convertDateToTimestamp(data.expectedOutboundDate)
        }

        // 添加车辆数据
        data.vehicles = [{
          brand: form.vehicleBrand,
          series: form.vehicleSeries,
          configName: form.vehicleConfig,
          colorCode: form.vehicleColorCode,
          sbPrice: form.vehicleSbPrice,
          quantity: 1,
          salePrice: form.salesAmount,
          skuId: form.skuId
        }]

        // 构建符合新API的请求数据（使用驼峰命名法）
        const orderPayload = {
          // 客户信息
          customerId: data.customerId,
          customerName: data.customerName,
          customerType: data.customerType,
          customerPhone: data.customerPhone,
          salesAgentName: data.salesAgentName, // 销售顾问姓名
          salesOrgName: data.salesOrgName, // 销售单位名称
          salesOrgId: data.salesOrgId, // 销售单位ID
          salesAgentId: data.salesAgentId || data.customerId, // 销售顾问ID，如果没有则使用客户ID
          salesLeaderId: data.salesLeaderId || data.customerId, // 销售主管ID，如果没有则使用客户ID
          salesStoreType: data.salesStoreType, // 销售地类型

          // SKU信息
          skuId: form.skuId,

          // 订单日期
          dealDate: data.dealDate, // 订单日期

          // 交付信息
          deliveryDate: data.expectedOutboundDate, // 交付日期
          deliveryOrgId: data.outboundOrgId, // 交付单位ID

          // 金额信息（转换为分）
          sbAmount: Math.round(form.sbAmount * 100), // 启票价格（分）
          invoiceAmount: Math.round(form.invoicePrice * 100), // 开票价格（分）
          salesAmount: Math.round(form.salesAmount * 100), // 销售价格（分）
          depositAmount: Math.round(data.depositAmount * 100), // 已付定金（分）
          depositDeductible: data.depositDeductible, // 定金是否转车款
          depositType: data.depositType || 'offline', // 定金类型
          discountAmount: Math.round(data.discountAmount * 100), // 优惠金额（分）
          discountDeductible: data.discountDeductible, // 优惠金额是否转车款
          hasExclusiveDiscount: data.hasExclusiveDiscount || 'NO', // 是否享受专项优惠补贴
          exclusiveDiscountType: data.exclusiveDiscountType || '', // 专享优惠类型
          exclusiveDiscountAmount: data.exclusiveDiscountAmount ? Math.round(data.exclusiveDiscountAmount * 100) : 0, // 专享优惠金额（分，保留兼容性）
          exclusiveDiscountPayableDeductible: data.exclusiveDiscountPayableDeductible || false, // 专享优惠是否转车款（保留兼容性）
          exclusiveDiscountReceivableAmount: data.exclusiveDiscountReceivableAmount ? Math.round(data.exclusiveDiscountReceivableAmount * 100) : 0, // 应收-厂家-专项优惠（分）
          exclusiveDiscountPayableAmount: data.exclusiveDiscountPayableAmount ? Math.round(data.exclusiveDiscountPayableAmount * 100) : 0, // 应付-客户-专项优惠（分）
          exclusiveDiscountPayableDeductible: data.exclusiveDiscountPayableDeductible || false, // 应付客户专项优惠是否转车款
          exclusiveDiscountRemark: data.exclusiveDiscountRemark || '', // 专项优惠备注
          dealAmount: Math.round(data.dealAmount * 100), // 成交总价（分）
          dealAmountCn: data.dealAmountCn, // 成交总价大写

          // 付款方式
          paymentMethod: data.paymentMethod || 'FULL', // 默认全款

          // 备注
          remark: data.remark || ''
        }

        // 如果选择了"有"车辆置换且填写了车牌号，添加二手车信息到请求数据
        if (data.hasUsedVehicle === 'YES' && data.usedVehicleId) {
          // 检查置换金额是否大于0
          if (!data.usedVehicleAmount || data.usedVehicleAmount <= 0) {
            return messages.error('二手车置换金额必须大于零')
          }

          orderPayload.usedVehicleId = data.usedVehicleId
          orderPayload.usedVehicleVin = data.usedVehicleVin
          orderPayload.usedVehicleAmount = Math.round(data.usedVehicleAmount * 100)
          orderPayload.usedVehicleDeductibleAmount = data.usedVehicleDeductibleAmount ? Math.round(data.usedVehicleDeductibleAmount * 100) : 0
          orderPayload.usedVehicleDiscountReceivableAmount = data.usedVehicleDiscountReceivableAmount ? Math.round(data.usedVehicleDiscountReceivableAmount * 100) : 0
          orderPayload.usedVehicleDiscountPayableAmount = data.usedVehicleDiscountPayableAmount ? Math.round(data.usedVehicleDiscountPayableAmount * 100) : 0
          orderPayload.usedVehicleDiscountPayableDeductible = data.usedVehicleDiscountPayableDeductible
          orderPayload.usedVehicleBrand = data.usedVehicleBrand
          orderPayload.usedVehicleModel = data.usedVehicleModel
          orderPayload.usedVehicleColor = data.usedVehicleColor
        }

        // 如果是贷款方式，添加贷款信息
        if (orderPayload.paymentMethod === 'LOAN') {
          orderPayload.loanChannel = data.loanChannel
          orderPayload.loanAmount = data.loanAmount ? Math.round(data.loanAmount * 100) : 0
          orderPayload.loanInitialAmount = data.loanInitialAmount ? Math.round(data.loanInitialAmount * 100) : 0
          orderPayload.loanMonths = data.loanMonths
          orderPayload.loanRebateAmount = data.loanRebateAmount ? Math.round(data.loanRebateAmount * 100) : 0
          orderPayload.loanRebatePayableDeductible = data.loanRebatePayableDeductible
          orderPayload.loanFee = data.loanFee ? Math.round(data.loanFee * 100) : 0
          orderPayload.loanRebateReceivableAmount = data.loanRebateReceivableAmount ? Math.round(data.loanRebateReceivableAmount * 100) : 0

          orderPayload.loanRebatePayableAmount = data.loanRebatePayableAmount ? Math.round(data.loanRebatePayableAmount * 100) : 0
          orderPayload.loanRebatePayableDeductible = data.loanRebatePayableDeductible
        }



        // 添加衍生收入信息
        orderPayload.hasDerivativeIncome = data.hasDerivativeIncome
        if (data.hasDerivativeIncome === 'YES') {
          // 将金额从元转换为分
          orderPayload.notaryFee = Math.round((data.notaryFee || 0) * 100)
          orderPayload.carefreeIncome = Math.round((data.carefreeIncome || 0) * 100)
          orderPayload.extendedWarrantyIncome = Math.round((data.extendedWarrantyIncome || 0) * 100)
          orderPayload.vpsIncome = Math.round((data.vpsIncome || 0) * 100)
          orderPayload.preInterest = Math.round((data.preInterest || 0) * 100)
          orderPayload.licensePlateFee = Math.round((data.licensePlateFee || 0) * 100)
          orderPayload.tempPlateFee = Math.round((data.tempPlateFee || 0) * 100)
          orderPayload.deliveryEquipment = Math.round((data.deliveryEquipment || 0) * 100)
          orderPayload.otherIncome = Math.round((data.otherIncome || 0) * 100)
        }

        // 添加赠品明细信息
        orderPayload.hasGiftItems = data.hasGiftItems
        if (data.hasGiftItems === 'YES' && Array.isArray(data.giftItems) && data.giftItems.length > 0) {
          // 处理赠品明细数据，将单价从元转换为分
          const giftItemsWithCents = data.giftItems.map(item => ({
            ...item,
            unitPrice: Math.round((item.unitPrice || 0) * 100) // 将单价从元转换为分
          }))
          // 将赠品数组转换为 JSON 字符串保存
          orderPayload.giftItems = JSON.stringify(giftItemsWithCents)
        } else {
          // 如果没有赠品，保存空字符串或 null
          orderPayload.giftItems = null
        }

        // 调用保存API
        const response = props.isEdit
          ? await vehicleOrderApi.updateOrder(data.id, orderPayload)
          : await vehicleOrderApi.createOrder(orderPayload)

        if (response.code === 200) {
          messages.success(props.isEdit ? '订单更新成功' : '订单创建成功')
          emit('save', response.data)
          modelVisible.value = false
        } else {
          messages.error(response.message || (props.isEdit ? '订单更新失败' : '订单创建失败'))
        }
      } catch (error) {
        console.error(props.isEdit ? '更新订单失败，请稍后重试' : '创建订单失败，请稍后重试')
      }
    })
  }

  // 处理取消
  const handleCancel = () => {
    modelVisible.value = false
    emit('cancel')
  }

  // 重置表单
  const resetForm = () => {
    // 重置出库单位选择器
    selectedOutboundOrg.value = null

    // 使用公共的默认值函数重置表单数据
    Object.assign(form, getFormDefaultValues())

    // 清空赠品表格
    if (giftItemsSectionRef.value?.giftItemsTableRef) {
      giftItemsSectionRef.value.giftItemsTableRef.clearRows()
    }
  }

  // 设置表单数据
  const setFormData = (data) => {
    const formData = { ...data }

    // 确保日期是时间戳格式
    if (formData.dealDate) {
      formData.dealDate = convertDateToTimestamp(formData.dealDate)
    }

    if (formData.expectedOutboundDate) {
      formData.expectedOutboundDate = convertDateToTimestamp(formData.expectedOutboundDate)
    }

    // 映射客户信息字段
    if (formData.mobile !== undefined) {
      formData.customerPhone = formData.mobile
    }
    if (formData.salesAgentName !== undefined) {
      formData.salesAgentName = formData.salesAgentName
    }
    if (formData.salesAgentId !== undefined) {
      formData.salesAgentId = formData.salesAgentId
    }
    if (formData.salesOrgName !== undefined) {
      formData.salesOrgName = formData.salesOrgName
    }
    if (formData.salesOrgId !== undefined) {
      formData.salesOrgId = formData.salesOrgId
    }
    if (formData.deliveryOrgName !== undefined) {
      formData.outboundOrgName = formData.deliveryOrgName
    }
    if (formData.deliveryOrgId !== undefined) {
      formData.outboundOrgId = formData.deliveryOrgId
    }

    // 映射车辆信息字段
    if (formData.skuId !== undefined) {
      formData.skuId = Number(formData.skuId) // 确保 skuId 是数字类型
    }
    if (formData.brand !== undefined) {
      formData.vehicleBrand = formData.brand
    }
    if (formData.series !== undefined) {
      formData.vehicleSeries = formData.series
    }
    if (formData.configName !== undefined) {
      formData.vehicleConfig = formData.configName
    }
    if (formData.color !== undefined) {
      formData.vehicleColorCode = formData.color
    }

    // 转换主要金额字段从分到元
    if (formData.dealAmount !== undefined && formData.dealAmount !== null) {
      formData.finalPrice = convertCentsToYuan(formData.dealAmount)
    }
    if (formData.salesAmount !== undefined && formData.salesAmount !== null) {
      formData.salesAmount = convertCentsToYuan(formData.salesAmount)
    }
    if (formData.sbAmount !== undefined && formData.sbAmount !== null) {
      formData.vehicleSbPrice = convertCentsToYuan(formData.sbAmount)
    }
    if (formData.invoiceAmount !== undefined && formData.invoiceAmount !== null) {
      formData.invoicePrice = convertCentsToYuan(formData.invoiceAmount)
    }

    // 临时处理：如果接口没有返回 salesAmount 和 invoiceAmount，使用 dealAmount 作为默认值
    // 这是为了兼容当前接口返回的数据结构
    if (formData.salesAmount === undefined && formData.invoiceAmount === undefined && formData.dealAmount !== undefined) {
      // 如果没有车辆售价和开票价格，但有成交总价，则使用成交总价作为默认值
      const dealAmountInYuan = convertCentsToYuan(formData.dealAmount)
      if (formData.salesAmount === undefined || formData.salesAmount === 0) {
        formData.salesAmount = dealAmountInYuan
      }
      if (formData.invoicePrice === undefined || formData.invoicePrice === 0) {
        formData.invoicePrice = dealAmountInYuan
      }
    }
    if (formData.depositAmount !== undefined && formData.depositAmount !== null) {
      formData.depositAmount = convertCentsToYuan(formData.depositAmount)
    }
    if (formData.discountAmount !== undefined && formData.discountAmount !== null) {
      formData.discountAmount = convertCentsToYuan(formData.discountAmount)
    }
    if (formData.grossProfitAmount !== undefined && formData.grossProfitAmount !== null) {
      formData.grossProfitAmount = convertCentsToYuan(formData.grossProfitAmount)
    }

    // 转换专项优惠相关金额字段从分到元
    if (formData.exclusiveDiscountAmount !== undefined && formData.exclusiveDiscountAmount !== null) {
      formData.exclusiveDiscountAmount = convertCentsToYuan(formData.exclusiveDiscountAmount)
    }
    if (formData.exclusiveDiscountReceivableAmount !== undefined && formData.exclusiveDiscountReceivableAmount !== null) {
      formData.exclusiveDiscountReceivableAmount = convertCentsToYuan(formData.exclusiveDiscountReceivableAmount)
    }
    if (formData.exclusiveDiscountPayableAmount !== undefined && formData.exclusiveDiscountPayableAmount !== null) {
      formData.exclusiveDiscountPayableAmount = convertCentsToYuan(formData.exclusiveDiscountPayableAmount)
    }

    // 设置金额字段默认值
    if (formData.depositAmount === undefined) formData.depositAmount = 0
    if (formData.depositDeductible === undefined) formData.depositDeductible = true
    if (formData.depositType === undefined) formData.depositType = 'offline'
    if (formData.totalSalePrice === undefined) formData.totalSalePrice = 0
    if (formData.discountAmount === undefined) formData.discountAmount = 0
    if (formData.discountDeductible === undefined) formData.discountDeductible = true
    if (formData.hasExclusiveDiscount === undefined) formData.hasExclusiveDiscount = 'NO'
    if (formData.exclusiveDiscountType === undefined) formData.exclusiveDiscountType = ''
    if (formData.exclusiveDiscountAmount === undefined) formData.exclusiveDiscountAmount = 0
    if (formData.exclusiveDiscountPayableDeductible === undefined) formData.exclusiveDiscountPayableDeductible = false
    if (formData.exclusiveDiscountReceivableAmount === undefined) formData.exclusiveDiscountReceivableAmount = 0
    if (formData.exclusiveDiscountPayableAmount === undefined) formData.exclusiveDiscountPayableAmount = 0
    if (formData.exclusiveDiscountPayableDeductible === undefined) formData.exclusiveDiscountPayableDeductible = true
    if (formData.exclusiveDiscountRemark === undefined) formData.exclusiveDiscountRemark = ''
    if (formData.dealAmount === undefined) formData.dealAmount = 0
    if (formData.dealAmountCn === undefined) formData.dealAmountCn = convertNumberToChinese(formData.dealAmount || 0)
    if (formData.totalSbPrice === undefined) formData.totalSbPrice = 0
    if (formData.invoicePrice === undefined) formData.invoicePrice = 0
    if (formData.profitRate === undefined) formData.profitRate = 0
    if (formData.grossProfitAmount === undefined) formData.grossProfitAmount = 0

    // 设置付款方式默认值
    if (formData.paymentMethod === undefined) formData.paymentMethod = 'FULL'

    // 设置贷款信息默认值
    if (formData.loanChannel === undefined) formData.loanChannel = ''
    if (formData.loanAmount === undefined) formData.loanAmount = 0
    if (formData.loanInitialAmount === undefined) formData.loanInitialAmount = 0
    if (formData.loanInitialRatio === undefined) formData.loanInitialRatio = 0
    if (formData.loanMonths === undefined) formData.loanMonths = null
    if (formData.loanRebateAmount === undefined) formData.loanRebateAmount = 0
    if (formData.loanRebatePayableDeductible === undefined) formData.loanRebatePayableDeductible = true
    if (formData.loanFee === undefined) formData.loanFee = 0
    if (formData.loanRebateReceivableAmount === undefined) formData.loanRebateReceivableAmount = 0
    if (formData.loanRebatePayableAmount === undefined) formData.loanRebatePayableAmount = 0
    if (formData.loanRebatePayableDeductible === undefined) formData.loanRebatePayableDeductible = true

    // 转换贷款首付比例从小数到百分比（例如：0.43 -> 43）
    if (formData.loanInitialRatio !== undefined && formData.loanInitialRatio !== null) {
      formData.loanInitialRatio = formData.loanInitialRatio * 100
    }

    // 转换贷款相关金额字段从分到元
    if (formData.loanAmount !== undefined && formData.loanAmount !== null) {
      formData.loanAmount = convertCentsToYuan(formData.loanAmount)
    }
    if (formData.loanInitialAmount !== undefined && formData.loanInitialAmount !== null) {
      formData.loanInitialAmount = convertCentsToYuan(formData.loanInitialAmount)
    }
    if (formData.loanRebateAmount !== undefined && formData.loanRebateAmount !== null) {
      formData.loanRebateAmount = convertCentsToYuan(formData.loanRebateAmount)
    }
    if (formData.loanFee !== undefined && formData.loanFee !== null) {
      formData.loanFee = convertCentsToYuan(formData.loanFee)
    }
    if (formData.loanRebateReceivableAmount !== undefined && formData.loanRebateReceivableAmount !== null) {
      formData.loanRebateReceivableAmount = convertCentsToYuan(formData.loanRebateReceivableAmount)
    }
    if (formData.loanRebatePayableAmount !== undefined && formData.loanRebatePayableAmount !== null) {
      formData.loanRebatePayableAmount = convertCentsToYuan(formData.loanRebatePayableAmount)
    }

    // 处理二手车置换信息
    // 当usedVehicleVin字段不为null时，车辆置换选项选中"是"，并填充usedVehicle相关字段值
    if (formData.usedVehicleVin && formData.usedVehicleVin.trim() !== '') {
      formData.hasUsedVehicle = 'YES'
    } else {
      // 如果没有VIN，设置为"否"
      if (formData.hasUsedVehicle === undefined) formData.hasUsedVehicle = 'NO'
    }

    // 设置二手车置换信息默认值
    if (formData.usedVehicleId === undefined) formData.usedVehicleId = ''
    if (formData.usedVehicleVin === undefined) formData.usedVehicleVin = ''
    if (formData.usedVehicleAmount === undefined) formData.usedVehicleAmount = 0
    if (formData.usedVehicleDeductibleAmount === undefined) formData.usedVehicleDeductibleAmount = 0
    if (formData.usedVehicleDiscountReceivableAmount === undefined) formData.usedVehicleDiscountReceivableAmount = 0
    if (formData.usedVehicleDiscountPayableAmount === undefined) formData.usedVehicleDiscountPayableAmount = 0
    if (formData.usedVehicleDiscountPayableDeductible === undefined) formData.usedVehicleDiscountPayableDeductible = true
    if (formData.usedVehicleBrand === undefined) formData.usedVehicleBrand = ''
    if (formData.usedVehicleModel === undefined) formData.usedVehicleModel = ''
    if (formData.usedVehicleColor === undefined) formData.usedVehicleColor = ''

    // 转换二手车相关金额字段从分到元
    if (formData.usedVehicleAmount !== undefined && formData.usedVehicleAmount !== null) {
      formData.usedVehicleAmount = convertCentsToYuan(formData.usedVehicleAmount)
    }
    if (formData.usedVehicleDeductibleAmount !== undefined && formData.usedVehicleDeductibleAmount !== null) {
      formData.usedVehicleDeductibleAmount = convertCentsToYuan(formData.usedVehicleDeductibleAmount)
    }
    if (formData.usedVehicleDiscountReceivableAmount !== undefined && formData.usedVehicleDiscountReceivableAmount !== null) {
      formData.usedVehicleDiscountReceivableAmount = convertCentsToYuan(formData.usedVehicleDiscountReceivableAmount)
    }
    if (formData.usedVehicleDiscountPayableAmount !== undefined && formData.usedVehicleDiscountPayableAmount !== null) {
      formData.usedVehicleDiscountPayableAmount = convertCentsToYuan(formData.usedVehicleDiscountPayableAmount)
    }





    // 设置赠品明细信息默认值
    if (formData.hasGiftItems === undefined) formData.hasGiftItems = 'NO'
    if (formData.giftItems === undefined) formData.giftItems = []

    // 处理赠品明细数据：如果是字符串格式，需要转换为数组
    if (formData.giftItems && typeof formData.giftItems === 'string') {
      try {
        // 尝试解析 JSON 字符串
        const parsedGiftItems = JSON.parse(formData.giftItems)
        if (Array.isArray(parsedGiftItems)) {
          // 转换赠品明细数据，将单价从分转换为元
          formData.giftItems = parsedGiftItems.map(item => ({
            ...item,
            unitPrice: item.unitPrice ? convertCentsToYuan(item.unitPrice) : 0
          }))
          // 如果有赠品数据，设置 hasGiftItems 为 'YES'
          if (formData.giftItems.length > 0) {
            formData.hasGiftItems = 'YES'
          }
        } else {
          // 如果解析结果不是数组，设置为空数组
          formData.giftItems = []
        }
      } catch (error) {
        console.warn('解析赠品明细 JSON 字符串失败:', error)
        formData.giftItems = []
      }
    } else if (Array.isArray(formData.giftItems)) {
      // 如果已经是数组格式，检查是否需要转换单价（从分到元）
      formData.giftItems = formData.giftItems.map(item => ({
        ...item,
        unitPrice: item.unitPrice ? convertCentsToYuan(item.unitPrice) : 0
      }))
      // 如果有赠品数据，设置 hasGiftItems 为 'YES'
      if (formData.giftItems.length > 0) {
        formData.hasGiftItems = 'YES'
      }
    }

    // 设置备注信息默认值
    if (formData.remark === undefined) formData.remark = ''
    if (formData.paymentRemark === undefined) formData.paymentRemark = ''

    // 设置客户类型默认值
    if (formData.customerType === undefined) formData.customerType = 'individual'

    // 设置销售地类型默认值
    if (formData.salesStoreType === undefined) formData.salesStoreType = '4S'

    Object.assign(form, formData)
  }

  // 处理子组件的验证请求
  const handleValidate = (fields, callback) => {
    if (!formRef.value) {
      callback?.(null)
      return
    }

    // 如果没有指定字段，验证整个表单
    if (!fields || fields.length === 0) {
      formRef.value.validate((errors) => {
        callback?.(errors)
      })
      return
    }

    // 验证指定字段
    formRef.value.validate(fields, (errors) => {
      callback?.(errors)
    })
  }

  // 返回对象（最终版本）
  return {
    // 响应式数据
    formRef,
    customerSelectorVisible,
    vehicleSelectorVisible,
    giftItemsSectionRef,
    remarkSectionRef,
    selectedOutboundOrg,
    form,
    rules,
    loanChannelOptions,
    loanMonthsOptions,
    modelVisible,
    currentPriceLimit,
    exclusiveDiscountConfig,

    // 基础方法
    updateVisible,
    showCustomerSelector,
    showVehicleSelector,

    // 计算函数
    calculateFinalPrice,
    calculateProfitRateAndAmount,

    // 事件处理函数
    handleVehicleSelected,
    handleInvoicePriceChange,
    handleSalePriceChange,
    handleDiscountChange,
    handleExclusiveDiscountChange,
    handleExclusiveDiscountReceivableAmountChange,
    handleExclusiveDiscountPayableAmountChange,
    handleExclusiveDiscountPayableDeductibleChange,
    handleLoanAmountChange,
    handleLoanInitialAmountChange,
    handleLoanFeeChange,
    handleLoanRebatePayableAmountChange,
    handleLoanRebatePayableDeductibleChange,
    handleUsedVehicleDeductibleAmountChange,
    handleUsedVehicleDiscountPayableAmountChange,
    handleUsedVehicleDiscountPayableDeductibleChange,

    handleOutboundOrgChange,
    handleCustomerSelected,

    // 保存和重置函数
    handleSave,
    handleCancel,
    resetForm,
    setFormData,
    handleValidate
  }
}
