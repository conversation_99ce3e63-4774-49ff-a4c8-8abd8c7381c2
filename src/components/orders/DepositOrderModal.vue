<template>
  <n-modal
    v-model:show="modelVisible"
    @update:show="updateVisible"
    :title="title"
    preset="card"
    style="width: 80%; max-width: 1200px"
    :mask-closable="false"
    transform-origin="center"
  >
    <n-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-placement="top"
      require-mark-placement="right-hanging"
      class="order-form"
    >
      <!-- 产品信息部分 -->
      <product-info-section
        :form="form"
        :config="productInfoConfig"
        :vehicle-select-error="vehicleSelectError"
        @show-vehicle-selector="vehicleSelectorVisible = true"
        @vehicle-selected="handleVehicleSelected"
        @update:form="handleFormUpdate"
      />
      <!-- 客户信息部分 -->
      <customer-info-section
        :form="form"
        :config="customerInfoConfig"
        @show-customer-selector="customerSelectorVisible = true"
      />

      <!-- 订单备注部分 -->
      <remark-section :form="form" :config="remarkConfig" />
    </n-form>
    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" @click="handleSave" :loading="saving"
          >确定</n-button
        >
      </n-space>
    </template>

    <!-- 客户选择器 -->
    <customer-selector
      v-model:visible="customerSelectorVisible"
      @select="handleCustomerSelected"
    />

    <!-- 车辆选择器 -->
    <vehicle-s-k-u-selector
      v-model:visible="vehicleSelectorVisible"
      @select="handleVehicleSelected"
    />
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from "vue";
import { NModal, NForm, NButton, NSpace } from "naive-ui";
import messages from "@/utils/messages";
import vehicleOrderApi from "@/api/vehicleOrder";
import CustomerSelector from "@/components/customer/CustomerSelector.vue";
import VehicleSKUSelector from "@/components/inventory/VehicleSKUSelector.vue";
import CustomerInfoSection from "@/components/orders/sections/CustomerInfoSection.vue";
import ProductInfoSection from "@/components/orders/sections/ProductInfoSection.vue";
import RemarkSection from "@/components/orders/sections/RemarkSection.vue";

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "新增定金订单",
  },
  initialData: {
    type: Object,
    default: () => ({}),
  },
});

// 定义组件事件
const emit = defineEmits(["update:visible", "save", "cancel"]);

// 表单引用
const formRef = ref(null);

// 不再需要定金状态选项

// 选择器可见性
const customerSelectorVisible = ref(false);
const vehicleSelectorVisible = ref(false);

// 保存中状态
const saving = ref(false);

// 车辆选择错误状态
const vehicleSelectError = ref(false);

// 客户信息区域配置 - 定金订单的客户信息配置
const customerInfoConfig = {
  visible: true,
  editable: true,
  "show-customer-selector": true,
  "search-deposits": false, // 定金订单中关闭自动查询定金功能，由用户手动输入
  fields: {
    customerName: { visible: true, editable: true },
    customerType: { visible: true, editable: true },
    customerPhone: { visible: true, editable: true },
    salesStoreType: { visible: false, editable: true }, // 隐藏销售地类型字段
    salesStoreDetail: { visible: false, editable: true }, // 隐藏销售地详情字段
    salesAgentName: { visible: true, editable: true },
    salesOrgName: { visible: true, editable: false },
    depositAmount: { visible: true, editable: true }, // 定金订单需要显示定金相关字段
    depositDeductible: { visible: false, editable: false }, // 隐藏定金转车款选项
    depositType: { visible: true, editable: true },
  },
};

// 产品信息区域配置 - 隐藏启票价格、开票价格、车辆售价、现金优惠及转车款选项
const productInfoConfig = {
  visible: true,
  editable: true,
  "show-vehicle-selector": true,
  fields: {
    dealDate: { visible: true, editable: true },
    vehicleBrand: { visible: true, editable: false },
    vehicleSeries: { visible: true, editable: false },
    vehicleConfig: { visible: true, editable: false },
    sbAmount: { visible: false, editable: false }, // 隐藏启票价格
    invoicePrice: { visible: false, editable: false }, // 隐藏开票价格
    salesAmount: { visible: false, editable: false }, // 隐藏车辆售价
    discountAmount: { visible: false, editable: false }, // 隐藏现金优惠
    discountDeductible: { visible: false, editable: false }, // 隐藏转车款选项
  },
};

// 备注区域配置 - 定金订单的备注配置
const remarkConfig = {
  visible: true,
  editable: true,
  fields: {
    remark: { visible: true, editable: true },
  },
};

// 表单数据
const form = reactive({
  id: null,
  customerId: null,
  customerName: "",
  customerType: "individual", // 客户类型，默认为个人客户
  customerPhone: "",
  customerAddress: "",
  salesAgentName: "",
  salesOrgId: null,
  salesOrgName: "",
  hasExclusiveDiscount: "NO", // 是否享受专项优惠补贴
  exclusiveDiscountType: null,
  exclusiveDiscountAmount: null,
  exclusiveDiscountReceivableAmount: 0, // 应收-厂家-专项优惠金额
  exclusiveDiscountPayableAmount: 0, // 应付-客户-专项优惠金额
  exclusiveDiscountPayableDeductible: true, // 应付客户专项优惠是否转车款
  exclusiveDiscountRemark: "", // 专项优惠备注
  remark: "",
  // 定金相关字段
  depositAmount: null,
  depositType: "offline", // 定金类型，默认线下付定
  depositDeductible: false,
  // 销售相关ID字段
  salesAgentId: null,
  salesLeaderId: null,
  salesStoreType: "4S", // 销售地类型，默认为单店
  // 产品信息字段
  skuId: null,
  vehicleBrand: "",
  vehicleSeries: "",
  vehicleConfig: "",
  sbAmount: null,
  invoicePrice: null,
  salesAmount: null,
  dealDate: null,
});

// 表单验证规则
const rules = {
  customerName: {
    required: true,
    message: "请输入客户名称",
    trigger: "blur",
    validator: (_, value) => {
      if (!value) {
        return new Error("请输入客户名称");
      }
      // 检查客户名称长度
      if (value.length > 20) {
        return new Error("客户名称不能超过20个字符");
      }
      return true;
    },
  },
  customerPhone: {
    required: true,
    message: "请输入联系电话",
    trigger: "blur",
    validator: (_, value) => {
      if (!value) {
        return new Error("请输入联系电话");
      }
      // 手机号码格式校验（支持11位手机号）
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(value)) {
        return new Error("请输入正确的手机号码格式");
      }
      return true;
    },
  },
  customerType: {
    required: true,
    message: "请选择客户类型",
    trigger: "blur",
  },
  salesStoreType: {
    required: true,
    message: "请选择销售地类型",
    trigger: "blur",
  },
  salesOrgName: {
    required: true,
    message: "请选择销售单位",
    trigger: "blur",
    validator: (_, value) => {
      if (!value) {
        return new Error("请选择销售顾问后自动填充销售单位");
      }
      return true;
    },
  },
  depositAmount: {
    required: true,
    type: "number",
    message: "已付定金必须大于0",
    trigger: ["blur", "change"],
    validator: (_, value) => {
      if (value === null || value === undefined || value === "") {
        return new Error("请输入已付定金");
      }
      if (typeof value !== "number") {
        return new Error("已付定金必须是数字");
      }
      if (value <= 0) {
        return new Error("已付定金必须大于0");
      }
      return true;
    },
  },
  depositType: {
    required: true,
    message: "请选择定金类型",
    trigger: "blur",
  },
  skuId: {
    required: true,
    message: "请选择意向车型",
    trigger: "blur",
    validator: (_, value) => {
      if (!value) {
        return new Error("请选择客户的意向车型");
      }
      return true;
    },
  },
  dealDate: {
    required: true,
    message: "请选择订单日期",
    trigger: "blur",
    validator: (_, value) => {
      if (!value) {
        return new Error("请选择订单日期");
      }
      return true;
    },
  },
  remark: {
    required: false,
    trigger: "blur",
    validator: (_, value) => {
      if (value && value.length > 500) {
        return new Error("备注内容不能超过500个字符");
      }
      return true;
    },
  },
  customerAddress: {
    required: false,
    trigger: "blur",
    validator: (_, value) => {
      if (value && value.length > 100) {
        return new Error("客户地址不能超过100个字符");
      }
      return true;
    },
  },
  salesAgentName: {
    required: false,
    trigger: "blur",
    validator: (_, value) => {
      if (value && value.length > 20) {
        return new Error("销售顾问姓名不能超过20个字符");
      }
      return true;
    },
  },
};

// 模态框可见性
const modelVisible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});

// 更新可见性
const updateVisible = (value) => {
  if (!value) {
    emit("cancel");
  }
};

// 监听模态框显示状态和编辑模式，处理表单初始化
watch(
  () => [props.visible, props.isEdit, props.initialData],
  ([visible, isEdit, initialData]) => {
    if (visible) {
      if (isEdit && initialData) {
        // 编辑模式：填充现有数据
        setFormData(initialData);
      } else {
        // 新增模式：重置表单
        resetForm();
      }
    }
  },
  { immediate: true }
);

// 处理客户选择
const handleCustomerSelected = (customer) => {
  messages.success(`已选择客户: ${customer.customerName}`);

  // 更新客户信息
  form.customerId = customer.id;
  form.customerName = customer.customerName;
  form.customerType = customer.customerType || "individual"; // 客户类型，默认为个人客户
  form.customerPhone = customer.mobile;
  form.customerAddress = customer.address;
  form.salesAgentName = customer.ownerSellerName;

  // 设置销售顾问和销售主管ID
  form.salesAgentId = customer.ownerSellerId || customer.id;
  form.salesLeaderId = customer.ownerLeaderId || customer.id;

  // 如果是新增，也更新销售单位
  if (!props.isEdit) {
    form.salesOrgId = customer.ownerOrgId || null;
    form.salesOrgName = customer.ownerOrgName || "";
  }
};

// 处理车辆选择
const handleVehicleSelected = (vehicle) => {
  if (!vehicle) return;

  // 清除车辆选择错误状态
  vehicleSelectError.value = false;

  messages.success(`已选择车型: ${vehicle.brand} ${vehicle.series}`);

  // 更新车辆信息
  form.skuId = vehicle.id;
  form.vehicleBrand = vehicle.brand || "";
  form.vehicleSeries = vehicle.series || "";
  form.vehicleConfig = vehicle.configName || "";
  form.sbAmount = vehicle.sbPrice || 0;
  // 默认销售价为启票价的1.35倍
  form.salesAmount = parseFloat((form.sbAmount * 1.35).toFixed(2));
  // 开票价格默认与车辆售价保持一致
  form.invoicePrice = form.salesAmount;
};

// 处理表单更新
const handleFormUpdate = (updatedForm) => {
  Object.assign(form, updatedForm);
};

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: null,
    customerId: null,
    customerName: "",
    customerType: "individual", // 重置为默认值
    customerPhone: "",
    customerAddress: "",
    salesAgentName: "",
    salesOrgId: null,
    salesOrgName: "",
    exclusiveDiscountType: null,
    exclusiveDiscountAmount: null,
    remark: "",
    // 定金相关字段
    depositAmount: null,
    depositType: "offline", // 重置时也设置默认值
    depositDeductible: false,
    // 销售相关ID字段
    salesAgentId: null,
    salesLeaderId: null,
    // 产品信息字段
    skuId: null,
    vehicleBrand: "",
    vehicleSeries: "",
    vehicleConfig: "",
    sbAmount: null,
    invoicePrice: null,
    salesAmount: null,
    dealDate: null,
  });
};

// 设置表单数据
const setFormData = (data) => {
  if (!data) return;

  Object.assign(form, {
    id: data.id,
    customerId: data.customerId,
    customerName: data.customerName,
    customerType: data.customerType || "individual", // 客户类型，默认为个人客户
    customerPhone: data.customerPhone || data.mobile,
    customerAddress: data.customerAddress || data.address,
    salesAgentName: data.salesAgentName || data.salesAgentName,
    salesOrgId: data.salesOrgId || data.salesOrgId,
    salesOrgName: data.salesOrgName || data.salesOrgName,
    exclusiveDiscountType: data.exclusiveDiscountType,
    exclusiveDiscountAmount: data.exclusiveDiscountAmount
      ? data.exclusiveDiscountAmount / 100
      : null, // 分转元
    remark: data.remark || "",
    // 定金相关字段
    depositAmount: data.depositAmount ? data.depositAmount / 100 : null, // 分转元
    depositType: data.depositType || "offline", // 设置定金类型，默认线下付定
    depositDeductible: data.depositDeductible || false,
    // 销售相关ID字段
    salesAgentId: data.salesAgentId || data.ownerSellerId,
    salesLeaderId: data.salesLeaderId || data.ownerLeaderId,
    // 产品信息字段
    skuId: data.skuId,
    vehicleBrand: data.vehicleBrand || "",
    vehicleSeries: data.vehicleSeries || "",
    vehicleConfig: data.vehicleConfig || "",
    sbAmount: data.sbAmount || null,
    invoicePrice: data.invoicePrice || null,
    salesAmount: data.salesAmount || null,
    dealDate: data.dealDate || null,
  });
};

// 处理取消
const handleCancel = () => {
  modelVisible.value = false;
  emit("cancel");
};

// 处理保存
const handleSave = () => {
  // 重置错误状态
  vehicleSelectError.value = false;

  // 检查是否选择了车辆
  if (!form.skuId) {
    vehicleSelectError.value = true;
    return;
  }

  // 检查是否选择了订单日期
  if (!form.dealDate) {
    messages.error("请选择订单日期");
    return;
  }

  formRef.value?.validate(async (errors) => {
    if (errors) {
      return;
    }

    saving.value = true;
    try {
      // 准备提交的数据
      const submitData = {
        ...form,
        // 将金额从元转为分
        exclusiveDiscountAmount: form.exclusiveDiscountAmount
          ? Math.round(form.exclusiveDiscountAmount * 100)
          : 0,
        exclusiveDiscountReceivableAmount:
          form.exclusiveDiscountReceivableAmount
            ? Math.round(form.exclusiveDiscountReceivableAmount * 100)
            : 0,
        exclusiveDiscountPayableAmount: form.exclusiveDiscountPayableAmount
          ? Math.round(form.exclusiveDiscountPayableAmount * 100)
          : 0,
        depositAmount: form.depositAmount
          ? Math.round(form.depositAmount * 100)
          : 0,
        sbAmount: form.sbAmount ? Math.round(form.sbAmount * 100) : 0,
        invoicePrice: form.invoicePrice
          ? Math.round(form.invoicePrice * 100)
          : 0,
        salesAmount: form.salesAmount ? Math.round(form.salesAmount * 100) : 0,
        orderType: "deposit", // 标记为定金订单
      };

      // 调用API保存数据
      const response = props.isEdit
        ? await vehicleOrderApi.updateDepositOrder(form.id, submitData)
        : await vehicleOrderApi.createDepositOrder(submitData);

      if (response.code === 200) {
        messages.success(
          props.isEdit ? "定金订单更新成功" : "定金订单创建成功"
        );
        modelVisible.value = false;
        emit("save");
      } else {
        messages.error(
          response.message || (props.isEdit ? "更新失败" : "创建失败")
        );
      }
    } catch (error) {
      console.error("保存定金订单失败:", error);
      messages.error("操作失败，请稍后重试");
    } finally {
      saving.value = false;
    }
  });
};

// 暴露方法给父组件
defineExpose({
  resetForm,
  setFormData,
});
</script>

<style lang="scss">
@use "@/styles/variables.scss" as *;
@use "@/styles/section-title.scss";
@use "@/styles/orderForm.scss";

.order-form {
  // 使用通用样式，这里只保留特定于定金订单表单的样式
  .option-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    flex-wrap: wrap;

    .option-label {
      margin-right: 16px;
      font-weight: 500;
    }

    .option-tip {
      margin-left: 16px;
      color: #666;
      font-size: 12px;
    }
  }

  .n-form-item-label {
    font-weight: 500;
    color: #333;
  }

  .n-form-item-feedback-wrapper {
    min-height: 18px;
  }

  .n-checkbox {
    white-space: nowrap;
  }
}

// 确保区域标题样式能够正确应用
.section-container {
  .section-title {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 8px;
    margin-top: 16px;

    .title-text {
      font-size: $font-size-large !important;
      font-weight: $font-weight-bold !important;
      color: $primary-color !important;
      display: flex !important;
      align-items: center !important;
      margin-right: 12px !important;

      &::before {
        content: "" !important;
        display: inline-block !important;
        width: 4px !important;
        height: 18px !important;
        background-color: $primary-color !important;
        margin-right: 8px !important;
        border-radius: $border-radius-small !important;
      }
    }

    .title-button {
      margin-left: 8px !important;
      height: 28px !important;
      padding: 0 12px !important;
    }
  }
}
</style>
