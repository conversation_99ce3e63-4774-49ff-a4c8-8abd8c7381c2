<template>
  <n-input-number
    v-bind="mergedProps"
    v-model:value="modelValue"
    @update:value="handleUpdate"
  />
</template>

<script setup>
import { computed } from 'vue'
import { NInputNumber } from 'naive-ui'
import { getNumberInputConfig } from '@/config/inputConfig'

// 定义组件属性
const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: null
  },
  type: {
    type: String,
    default: 'default', // amount, quantity, percentage, price
    validator: (value) => ['default', 'amount', 'quantity', 'percentage', 'price'].includes(value)
  },
  min: {
    type: Number,
    default: undefined
  },
  max: {
    type: Number,
    default: undefined
  },
  precision: {
    type: Number,
    default: undefined
  },
  step: {
    type: Number,
    default: undefined
  },
  placeholder: {
    type: String,
    default: undefined
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'medium'
  },
  showButton: {
    type: Boolean,
    default: true
  }
})

// 定义事件
const emit = defineEmits(['update:modelValue'])

// 合并配置
const mergedProps = computed(() => {
  const config = getNumberInputConfig(props.type)
  
  return {
    ...config,
    // 组件传入的属性优先级更高
    min: props.min !== undefined ? props.min : config.min,
    max: props.max !== undefined ? props.max : config.max,
    precision: props.precision !== undefined ? props.precision : config.precision,
    step: props.step !== undefined ? props.step : config.step,
    placeholder: props.placeholder !== undefined ? props.placeholder : config.placeholder,
    disabled: props.disabled,
    readonly: props.readonly,
    size: props.size,
    showButton: props.showButton
  }
})

// 处理值更新
const handleUpdate = (value) => {
  emit('update:modelValue', value)
}
</script>
